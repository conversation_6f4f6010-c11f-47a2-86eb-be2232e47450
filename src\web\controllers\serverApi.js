const getStations =
    async (req) => {
  const response = await _get(req, 'stations/all');
  if (!response.ok) throw new Error('Failed to get stations');
  return await response.json();
}

const getLockStations =
    async (req) => {
  const response = await _get(req, 'lockstations/all');
  if (!response.ok) throw new Error('Failed to get lock stations');
  return await response.json();
}

const getDevices =
    async (req) => {
  const response = await _get(req, 'devices/all');
  if (!response.ok) throw new Error('Failed to get devices');
  return await response.json();
}

const getDevicesPosition =
    async (req) => {
  const response = await _get(req, 'devices/position');
  if (!response.ok) throw new Error('Failed to get devices positions');
  return await response.json();
}

const getDeviceModels =
    async (req) => {
  const response = await _get(req, 'devices/models/all');
  if (!response.ok) throw new Error('Failed to get device models');
  return await response.json();
}

const createDevice =
    async (req, {serial, qrCode, type, modelId, phone, description}) => {
  if (!serial) throw new Error('serial is required');
  if (!qrCode) throw new Error('qrCode is required');
  if (!type) throw new Error('type is required');
  if (!modelId) throw new Error('modelId is required');
  if (!phone) throw new Error('phone is required');
  if (!description) throw new Error('description is required');

  const response = await _post(req, 'devices', {
    body: {serial, qrCode, modelId, phone, type, description},
  });
  if (!response.ok) throw new Error('Failed to create device');
}

const deleteDevice =
    async (req, id) => {
  const response = await _delete(req, `devices?id=${id}`);
  if (!response.ok) throw new Error('Failed to delete device');
}

const getTotalUsers =
    async (req) => {
  const response = await _get(req, 'users/count');
  if (!response.ok) throw new Error('Failed to get users count');
  const json = await response.json();
  return json['total'];
}

const getRidesPerHour =
    async (req) => {
  const response = await _get(req, 'devices/rent/rides-per-hour');
  if (!response.ok) throw new Error('Failed to get rides per hour');
  return await response.json();
}

async function _get(req, path) {
  return await _request(req, 'GET', path);
}

async function _post(req, path, {body = {}}) {
  return await _request(req, 'POST', path, {
    body: body,
  });
}

async function _delete(req, path) {
  return await _request(req, 'DELETE', path);
}

async function _request(req, method, path, {
  body = {},
  headers = {},
} = {}) {
  const options = {
    method,
    headers: _getHeaders(req, headers),
  };
  if (Object.keys(body).length > 0) {
    options.body = JSON.stringify(body);
  }
  return await fetch(`http://localhost:8080/${path}`, options);
}

function _getHeaders(req, userHeaders) {
  const token = req?.cookies?.accessToken;
  const headers = {
    'Content-Type': 'application/json',
    ...userHeaders,
  };
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }
  return headers;
}

export default {
  getStations,
  getLockStations,
  getDevices,
  getDevicesPosition,
  getDeviceModels,
  createDevice,
  deleteDevice,
  getTotalUsers,
  getRidesPerHour
};