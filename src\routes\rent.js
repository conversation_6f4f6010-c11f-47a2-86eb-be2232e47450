import express from 'express';
import rentController from '../controllers/rent.js';
import authenticate from '../middlewares/authenticate.js';

const router = express.Router();

router.post('/', authenticate.authenticateToken, rentController.rentDevice);
router.get('/startTime', authenticate.authenticateToken, rentController.getRentStartTime);
router.get('/rides-per-hour', authenticate.authenticateToken, rentController.getRidesPerHour);
router.post('/debugCreateRent', authenticate.authenticateToken, rentController.debugCreateRent);

export default router;